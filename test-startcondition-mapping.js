// Test script to verify startCondition mapping functions
import { workflowStartConditions, getStartConditionLabel, getStartConditionValue } from './shared/enums.ts'

console.log('Testing startCondition mappings...')

// Test the mapping object
console.log('workflowStartConditions:', workflowStartConditions)

// Test getStartConditionLabel function
console.log('beforeCheckin label:', getStartConditionLabel('beforeCheckin'))
console.log('afterCheckin label:', getStartConditionLabel('afterCheckin'))
console.log('beforeCheckout label:', getStartConditionLabel('beforeCheckout'))
console.log('whenInquiryReceived label:', getStartConditionLabel('whenInquiryReceived'))
console.log('whenCartAbandoned label:', getStartConditionLabel('whenCartAbandoned'))

// Test getStartConditionValue function (backward compatibility)
console.log('Before Check In value:', getStartConditionValue('Before Check In'))
console.log('After Check In value:', getStartConditionValue('After Check In'))
console.log('Before Check Out value:', getStartConditionValue('Before Check Out'))
console.log('When Inquiry Received value:', getStartConditionValue('When Inquiry Received'))
console.log('When Cart Abandoned value:', getStartConditionValue('When Cart Abandoned'))

// Test with unknown values
console.log('Unknown label:', getStartConditionLabel('unknown'))
console.log('Unknown value:', getStartConditionValue('Unknown Label'))

console.log('All tests completed!')
