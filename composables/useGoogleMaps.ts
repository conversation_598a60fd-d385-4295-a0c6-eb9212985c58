interface GoogleMapsWindow extends Window {
  google?: {
    maps: {
      places: {
        AutocompleteService: new () => google.maps.places.AutocompleteService
        PlacesService: new (element: HTMLElement) => google.maps.places.PlacesService
      }
    }
  }
  initGoogleMapsCallback?: () => void
}

declare const window: GoogleMapsWindow

export function useGoogleMaps() {
  const isScriptLoaded = ref(false)
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  const PLACES_API_KEY = 'AIzaSyBKIBWct7YA_5Xxb9Dy-CDaw5f-0XBjDMs'

  async function loadGoogleMapsScript(): Promise<void> {
    if (isScriptLoaded.value) {
      console.log('Google Maps script already loaded')
      return
    }

    if (isLoading.value) {
      // Wait for the current loading to complete
      return new Promise((resolve) => {
        const checkLoaded = () => {
          if (isScriptLoaded.value || !isLoading.value) {
            resolve()
          } else {
            setTimeout(checkLoaded, 100)
          }
        }
        checkLoaded()
      })
    }

    console.log('Loading Google Maps script...')
    isLoading.value = true
    error.value = null

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${PLACES_API_KEY}&libraries=places&callback=initGoogleMapsCallback`
    script.async = true
    script.defer = true

    return new Promise((resolve, reject) => {
      window.initGoogleMapsCallback = () => {
        console.log('Google Maps script loaded successfully')
        isScriptLoaded.value = true
        isLoading.value = false
        resolve()
      }

      script.onerror = (errorEvent) => {
        console.error('Error loading Google Maps script:', errorEvent)
        error.value = 'Failed to load Google Maps script'
        isLoading.value = false
        reject(new Error('Failed to load Google Maps script'))
      }

      document.head.appendChild(script)
    })
  }

  function getPlacePredictions(input: string): Promise<google.maps.places.AutocompletePrediction[]> {
    return new Promise((resolve, reject) => {
      if (!window.google?.maps?.places) {
        reject(new Error('Google Maps Places API not loaded'))
        return
      }

      const service = new window.google.maps.places.AutocompleteService()
      
      service.getPlacePredictions(
        {
          input,
          types: ['address'],
          componentRestrictions: { country: [] }, // Allow all countries
        },
        (predictions, status) => {
          if (status === window.google!.maps.places.PlacesServiceStatus.OK && predictions) {
            resolve(predictions)
          } else if (status === window.google!.maps.places.PlacesServiceStatus.ZERO_RESULTS) {
            resolve([])
          } else {
            reject(new Error(`Places API error: ${status}`))
          }
        }
      )
    })
  }

  function getPlaceDetails(placeId: string): Promise<google.maps.places.PlaceResult> {
    return new Promise((resolve, reject) => {
      if (!window.google?.maps?.places) {
        reject(new Error('Google Maps Places API not loaded'))
        return
      }

      // Create a temporary div for the PlacesService
      const tempDiv = document.createElement('div')
      const service = new window.google.maps.places.PlacesService(tempDiv)

      service.getDetails(
        {
          placeId,
          fields: [
            'formatted_address',
            'address_components',
            'geometry',
            'name',
            'place_id'
          ]
        },
        (place, status) => {
          if (status === window.google!.maps.places.PlacesServiceStatus.OK && place) {
            resolve(place)
          } else {
            reject(new Error(`Place details error: ${status}`))
          }
        }
      )
    })
  }

  return {
    isScriptLoaded: readonly(isScriptLoaded),
    isLoading: readonly(isLoading),
    error: readonly(error),
    loadGoogleMapsScript,
    getPlacePredictions,
    getPlaceDetails
  }
}
