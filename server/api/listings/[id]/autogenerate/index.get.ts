import { findCardContentLocal } from '~/services/ai'
import { getListing } from '~/services/db'

export default defineEventHandler(async (event) => {
  const listingId = getRouterParam(event, 'id')

  // Extract the type from query parameters
  const query = getQuery(event)
  const type = query.type as string

  // Validate required parameters
  if (!listingId) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Listing ID is required',
    })
  }

  if (!type) {
    throw createError({
      statusCode: 400,
      statusMessage: 'Type parameter is required',
    })
  }

  const listing = await getListing(listingId)

  switch (type) {
    case 'localRecommendations': {
      const localCards = await findCardContentLocal(listing)
      return localCards
    }
    case 'fromMessages': {
      console.log('handling messages')
      break
    }
    case 'fromPmsContent': {
      console.log('handling pms content')
      break
    }
    default: {
      throw createError({
        statusCode: 400,
        statusMessage: 'Invalid autogenerate type',
      })
    }
  }

  // Your autogenerate logic here
  return {
    listingId,
    type,
    message: `Autogenerating for listing ${listingId} with type ${type}`,
  }
})
