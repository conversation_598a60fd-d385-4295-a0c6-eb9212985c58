<script setup lang="ts">
interface Card {
  card: string
  text: string
}

const props = defineProps({
  cards: {
    type: Array as () => Card[],
    required: true,
  },
})

const emit = defineEmits<{ close: [value: Card[] | boolean] }>()

// Create reactive refs for each card's text content
const cardTexts = ref<Record<string, string>>({})

// Create reactive array of cards that can be modified
const editableCards = ref<Card[]>([])

// Initialize card texts and editable cards from props
onMounted(() => {
  editableCards.value = [...props.cards]
  props.cards.forEach((card) => {
    cardTexts.value[card.card] = card.text
  })
})

// Delete a card
function deleteCard(cardId: string) {
  editableCards.value = editableCards.value.filter(card => card.card !== cardId)
  delete cardTexts.value[cardId]
}

// Save changes and emit the updated cards
function saveChanges() {
  // Create updated cards array with the modified text content
  const updatedCards = editableCards.value.map(card => ({
    card: card.card,
    text: cardTexts.value[card.card] || card.text,
  }))

  emit('close', updatedCards)
}

// Format card ID for display (convert underscores to spaces and capitalize)
function formatCardTitle(cardId: string) {
  return cardId
    .replace(/^faq_/, '') // Remove 'faq_' prefix
    .replace(/_/g, ' ') // Replace underscores with spaces
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}
</script>

<template>
  <UModal :close="{ onClick: () => emit('close', false) }" title="Edit FAQ Cards">
    <template #body>
      <div class="space-y-6">
        <div v-for="card in editableCards" :key="card.card" class="space-y-2">
          <!-- Card Header with Title and Delete Button -->
          <div class="flex items-center justify-between">
            <div>
              <h3 class="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {{ formatCardTitle(card.card) }}
              </h3>
              <!-- Card ID (smaller, muted) -->
              <p class="text-sm text-gray-500 dark:text-gray-400">
                ID: {{ card.card }}
              </p>
            </div>

            <!-- Delete Button -->
            <UButton icon="i-lucide-trash-2" color="error" variant="ghost" size="sm" :ui="{ rounded: 'rounded-full' }"
              class="hover:bg-red-50 dark:hover:bg-red-900/20" @click="deleteCard(card.card)" />
          </div>

          <!-- Textarea for card content -->
          <UTextarea v-model="cardTexts[card.card]" :placeholder="`Enter content for ${formatCardTitle(card.card)}...`"
            :rows="6" autoresize :maxrows="12" class="w-full" />
        </div>

        <!-- Empty state when no cards -->
        <div v-if="editableCards.length === 0" class="text-center py-8">
          <p class="text-gray-500 dark:text-gray-400">
            No cards remaining. All cards have been deleted.
          </p>
        </div>
      </div>
    </template>
    <template #footer>
      <div class="flex gap-2">
        <UButton color="neutral" label="Cancel" @click="emit('close', false)" />
        <UButton label="Save Changes" @click="saveChanges" />
      </div>
    </template>
  </UModal>
</template>
