<script setup lang="ts">
import { onMounted, onUnmounted, ref, watch } from 'vue'

const props = defineProps<{
  fullAddress: string
}>()

const emit = defineEmits<{
  close: [{ success: boolean, address?: any }]
}>()

// Simple debounce function
function debounce<T extends (...args: any[]) => any>(func: T, wait: number): (...args: Parameters<T>) => void {
  let timeout: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

const { loadGoogleMapsScript, getPlacePredictions, getPlaceDetails, isLoading: mapsLoading } = useGoogleMaps()

// Form state
const addressInput = ref(props.fullAddress || '')
const suggestions = ref<any[]>([])
const isLoadingSuggestions = ref(false)
const selectedPlace = ref<any>(null)
const showSuggestions = ref(false)

// Loading and error states
const isSubmitting = ref(false)
const error = ref<string | null>(null)

// Debounced search function
const debouncedSearch = debounce(async (query: string) => {
  if (!query || query.length < 3) {
    suggestions.value = []
    showSuggestions.value = false
    return
  }

  try {
    isLoadingSuggestions.value = true
    error.value = null

    const predictions = await getPlacePredictions(query)
    suggestions.value = predictions.map(prediction => ({
      label: prediction.description,
      value: prediction.place_id,
      structured_formatting: prediction.structured_formatting,
    }))
    showSuggestions.value = true
  }
  catch (err) {
    console.error('Error fetching suggestions:', err)
    error.value = 'Failed to fetch address suggestions'
    suggestions.value = []
  }
  finally {
    isLoadingSuggestions.value = false
  }
}, 300)

// Watch for input changes
watch(addressInput, (newValue) => {
  if (newValue !== selectedPlace.value?.formatted_address) {
    selectedPlace.value = null
    debouncedSearch(newValue)
  }
})

// Close suggestions when clicking outside
function handleClickOutside() {
  showSuggestions.value = false
}

// Add click outside listener
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

// Handle suggestion selection
async function onSuggestionSelect(suggestion: any) {
  try {
    isLoadingSuggestions.value = true
    error.value = null

    const placeDetails = await getPlaceDetails(suggestion.value)
    selectedPlace.value = placeDetails
    addressInput.value = placeDetails.formatted_address || suggestion.label
    showSuggestions.value = false
  }
  catch (err) {
    console.error('Error fetching place details:', err)
    error.value = 'Failed to fetch address details'
  }
  finally {
    isLoadingSuggestions.value = false
  }
}

// Handle form submission
async function handleSave() {
  if (!addressInput.value.trim()) {
    error.value = 'Please enter an address'
    return
  }

  isSubmitting.value = true

  try {
    // If we have a selected place with details, use that
    // Otherwise, just use the input text
    const addressData = selectedPlace.value
      ? {
        fullAddress: selectedPlace.value.formatted_address,
        placeId: selectedPlace.value.place_id,
        geometry: selectedPlace.value.geometry,
        addressComponents: selectedPlace.value.address_components,
      }
      : {
        fullAddress: addressInput.value.trim(),
      }

    emit('close', { success: true, address: addressData })
  }
  catch (err) {
    console.error('Error saving address:', err)
    error.value = 'Failed to save address'
  }
  finally {
    isSubmitting.value = false
  }
}

// Initialize Google Maps on component mount
onMounted(async () => {
  try {
    await loadGoogleMapsScript()
  }
  catch (err) {
    console.error('Failed to load Google Maps:', err)
    error.value = 'Failed to load address autocomplete. You can still enter an address manually.'
  }
})
</script>

<template>
  <UModal :close="{ onClick: () => emit('close', { success: false }) }" title="Update Listing Address">
    <template #body>
      <div class="space-y-4">
        <!-- Address Input with Autocomplete -->
        <div class="space-y-2">
          <label class="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Address
          </label>

          <div class="relative">
            <UInput v-model="addressInput" placeholder="Start typing an address..."
              :loading="isLoadingSuggestions || mapsLoading" class="w-full"
              @focus="showSuggestions = suggestions.length > 0" />

            <!-- Suggestions Dropdown -->
            <div v-if="showSuggestions && suggestions.length > 0"
              class="absolute z-50 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg max-h-60 overflow-auto"
              @click.stop>
              <div v-for="suggestion in suggestions" :key="suggestion.value"
                class="px-4 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-b-0"
                @click="onSuggestionSelect(suggestion)">
                <div class="text-sm font-medium text-gray-900 dark:text-gray-100">
                  {{ suggestion.structured_formatting?.main_text || suggestion.label }}
                </div>
                <div class="text-xs text-gray-500 dark:text-gray-400">
                  {{ suggestion.structured_formatting?.secondary_text || '' }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="error" class="text-sm text-red-600 dark:text-red-400">
          {{ error }}
        </div>

        <!-- Selected Place Info -->
        <div v-if="selectedPlace" class="p-3 bg-green-50 dark:bg-green-900/20 rounded-md">
          <div class="flex items-center gap-2">
            <UIcon name="i-lucide-map-pin" class="text-green-600 dark:text-green-400" />
            <span class="text-sm text-green-800 dark:text-green-200">
              Address verified with Google Places
            </span>
          </div>
        </div>
      </div>
    </template>

    <template #footer>
      <div class="flex gap-2">
        <UButton color="neutral" label="Cancel" :disabled="isSubmitting" @click="emit('close', { success: false })" />
        <UButton label="Save Address" :loading="isSubmitting" :disabled="!addressInput.trim() || isSubmitting"
          @click="handleSave" />
      </div>
    </template>
  </UModal>
</template>
