import { createOpenAI } from '@ai-sdk/openai'
import { generateText } from 'ai'

const apiKey = useRuntimeConfig().openaiApiKey
if (!apiKey)
    throw new Error('Missing OpenAI API key')
const openai = createOpenAI({
    apiKey,
})

export async function findCardContentLocal(listing): Promise<any> {
    console.info('inside function findLocal', { l: listing })
    let address = listing.address?.fullAddress

    // if (listing?.integrations?.hostaway && listing?.address?.streetAddressLine1) {
    //   address = `${listing.address.streetAddressLine1}, ${listing.address.city}, ${
    //     listing.address.country
    //   }`;
    // }

    if (!address) {
        console.warn('Unable to determine the address')
        return []
    }

    console.info('got the address', { a: address })

    const cards = [
        { id: 'faq_airport', prompt: 'airports' },
        { id: 'faq_train', prompt: 'trains' },
        { id: 'faq_bus', prompt: 'buses' },
        { id: 'faq_cab', prompt: 'cabs and ride sharing services' },
        { id: 'faq_restaurants', prompt: 'restaurants' },
        { id: 'faq_nightlife', prompt: 'nightlife and nightclubs' },
        { id: 'faq_bars', prompt: 'bars' },
        { id: 'faq_cafes', prompt: 'cafes, coffee shops, and breakfast spots' },
        { id: 'faq_groceryStores', prompt: 'grocery stores' },
        { id: 'faq_attractions', prompt: 'local attractions' },
        { id: 'faq_museums', prompt: 'museums' },
        { id: 'faq_outdoor', prompt: 'outdoor activities and parks' },
    ]

    const res = []

    const promises = cards.map(async (c) => {
        const prompt = `You are talking directly to a traveler. Tell him about nearby ${c.prompt} to this address. Be detailed but succinct, but don't reference the address in the answer. Simply describe your local recommendations for ${c.prompt}. Address: ${address}`
        try {
            const { text } = await generateText({
                model: openai('gpt-4.1-mini'),
                prompt,
            })
            return { card: c.id, text }
        }
        catch (e) {
            console.error('Unable to generate relevant content', { error: e })
            return null // Return null in case of error
        }
    })

    const results = await Promise.all(promises) // Wait for all promises to resolve
    res.push(...results.filter(result => result !== null)) // Filter out null results
    return res
}
